﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("WinFormsApp1.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_05_21_14_40_41() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-05-21_14-40-41", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_05_21_14_40_52() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-05-21_14-40-52", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_05_21_15_42_10() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-05-21_15-42-10", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_18_07_35() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_18-07-35", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_18_07_38() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_18-07-38", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_18_07_41() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_18-07-41", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_18_07_411() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_18-07-411", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_18_07_44() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_18-07-44", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_19_18_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_19-18-16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_19_18_18() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_19-18-18", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_19_18_181() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_19-18-181", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_19_18_26() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_19-18-26", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_19_19_19() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_19-19-19", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_10_19_31_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-10_19-31-24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_22_00_16_45() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-22_00-16-45", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_22_00_16_48() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-22_00-16-48", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_26() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-26", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_29() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-29", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_34() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-34", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_35() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-35", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_36() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-36", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_38() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-38", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_381() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-381", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_39() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-39", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_40() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-40", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_41() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-41", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_36_411() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-36-411", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_38_40() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-38-40", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_43_38() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-43-38", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_53_58() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-53-58", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_19_53_581() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_19-53-581", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_05_04() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-05-04", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_05_041() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-05-041", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_10_58() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-10-58", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_27_35() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-27-35", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_27_38() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-27-38", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_27_40() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-27-40", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_31_35() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-31-35", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_44_40() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-44-40", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_44_42() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-44-42", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_26_20_45_55() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-26_20-45-55", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_27_00_07_10() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-27_00-07-10", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo_2025_06_27_00_07_13() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_2025-06-27_00-07-13", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
