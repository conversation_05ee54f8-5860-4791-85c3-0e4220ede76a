﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Subscriber
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Subscriber))
        sup_id = New TextBox()
        source_income = New TextBox()
        family_total = New TextBox()
        TextBox_workplace = New TextBox()
        sup_living = New TextBox()
        sup_number = New TextBox()
        sup_age = New TextBox()
        sup_name = New TextBox()
        sup_pasport = New TextBox()
        RadioButton_libyan = New RadioButton()
        RadioButton_nonlibyan = New RadioButton()
        RadioButton_stability2 = New RadioButton()
        RadioButton_stability1 = New RadioButton()
        RadioButton_living3 = New RadioButton()
        RadioButtonliving2 = New RadioButton()
        RadioButton_living1 = New RadioButton()
        RadioButton_stability3 = New RadioButton()
        RadioButton_working = New RadioButton()
        RadioButton_nonworking = New RadioButton()
        CheckBox_sikePressure = New CheckBox()
        CheckBox_sikeSuger = New CheckBox()
        CheckBox_sikeSly = New CheckBox()
        CheckBox_sikeHind = New CheckBox()
        CheckBox_sikeBenignant = New CheckBox()
        RadioButton_fatherfamily_no = New RadioButton()
        RadioButton_fatherfamily_yes = New RadioButton()
        Label1 = New Label()
        Label2 = New Label()
        Label3 = New Label()
        Label4 = New Label()
        Label5 = New Label()
        Label6 = New Label()
        Label7 = New Label()
        Label8 = New Label()
        Label9 = New Label()
        Label_typeneeds = New Label()
        GroupBox_suptype = New GroupBox()
        GroupBox_stability_living = New GroupBox()
        GroupBox_work = New GroupBox()
        GroupBox_j = New GroupBox()
        GroupBox_fatherfamily = New GroupBox()
        GroupBox_helpfamilly = New GroupBox()
        RadioButton_helpfamilly_yes = New RadioButton()
        RadioButton_helpfamilly_no = New RadioButton()
        GroupBoxsike = New GroupBox()
        RadioButton_sikeyes = New RadioButton()
        RadioButton_sikeno = New RadioButton()
        medical_insurance = New GroupBox()
        medical_insurance_yes = New RadioButton()
        medical_insurance_no = New RadioButton()
        TextBox_istability = New TextBox()
        Button_register_save = New Button()
        CheckBox_medicine = New CheckBox()
        CheckBox_eat = New CheckBox()
        CheckBox_clothes = New CheckBox()
        CheckBox_money = New CheckBox()
        Button_register_delete = New Button()
        GroupBox_sik = New GroupBox()
        TextBox_item = New TextBox()
        item_quntity = New TextBox()
        TextBox_cloth = New TextBox()
        cloth_quntity = New TextBox()
        med_quntity = New TextBox()
        TextBox_med = New TextBox()
        TextBox_moneyy = New TextBox()
        PictureBox1 = New PictureBox()
        PictureBox2 = New PictureBox()
        PictureBox3 = New PictureBox()
        PictureBox4 = New PictureBox()
        PictureBox5 = New PictureBox()
        PictureBox6 = New PictureBox()
        PictureBox7 = New PictureBox()
        PictureBox8 = New PictureBox()
        PictureBox9 = New PictureBox()
        PictureBox10 = New PictureBox()
        PictureBox11 = New PictureBox()
        PictureBox12 = New PictureBox()
        GroupBox_suptype.SuspendLayout()
        GroupBox_stability_living.SuspendLayout()
        GroupBox_work.SuspendLayout()
        GroupBox_j.SuspendLayout()
        GroupBox_fatherfamily.SuspendLayout()
        GroupBox_helpfamilly.SuspendLayout()
        GroupBoxsike.SuspendLayout()
        medical_insurance.SuspendLayout()
        GroupBox_sik.SuspendLayout()
        CType(PictureBox1, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox2, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox3, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox4, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox5, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox6, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox7, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox8, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox9, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox10, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox11, ComponentModel.ISupportInitialize).BeginInit()
        CType(PictureBox12, ComponentModel.ISupportInitialize).BeginInit()
        SuspendLayout()
        ' 
        ' sup_id
        ' 
        sup_id.BackColor = Color.Azure
        sup_id.Location = New Point(56, 49)
        sup_id.Name = "sup_id"
        sup_id.Size = New Size(184, 27)
        sup_id.TabIndex = 0
        ' 
        ' source_income
        ' 
        source_income.BackColor = Color.Azure
        source_income.Location = New Point(386, 47)
        source_income.Name = "source_income"
        source_income.Size = New Size(184, 27)
        source_income.TabIndex = 1
        ' 
        ' family_total
        ' 
        family_total.BackColor = Color.Azure
        family_total.Location = New Point(49, 814)
        family_total.Name = "family_total"
        family_total.Size = New Size(184, 27)
        family_total.TabIndex = 2
        ' 
        ' TextBox_workplace
        ' 
        TextBox_workplace.BackColor = Color.Azure
        TextBox_workplace.Location = New Point(49, 755)
        TextBox_workplace.Name = "TextBox_workplace"
        TextBox_workplace.Size = New Size(184, 27)
        TextBox_workplace.TabIndex = 3
        ' 
        ' sup_living
        ' 
        sup_living.BackColor = Color.Azure
        sup_living.Location = New Point(54, 429)
        sup_living.Name = "sup_living"
        sup_living.Size = New Size(184, 27)
        sup_living.TabIndex = 4
        ' 
        ' sup_number
        ' 
        sup_number.BackColor = Color.Azure
        sup_number.Location = New Point(54, 373)
        sup_number.Name = "sup_number"
        sup_number.Size = New Size(184, 27)
        sup_number.TabIndex = 5
        ' 
        ' sup_age
        ' 
        sup_age.BackColor = Color.Azure
        sup_age.Location = New Point(56, 310)
        sup_age.Name = "sup_age"
        sup_age.Size = New Size(184, 27)
        sup_age.TabIndex = 6
        ' 
        ' sup_name
        ' 
        sup_name.BackColor = Color.Azure
        sup_name.Location = New Point(56, 249)
        sup_name.Name = "sup_name"
        sup_name.Size = New Size(184, 27)
        sup_name.TabIndex = 7
        ' 
        ' sup_pasport
        ' 
        sup_pasport.BackColor = Color.Azure
        sup_pasport.Location = New Point(56, 198)
        sup_pasport.Name = "sup_pasport"
        sup_pasport.Size = New Size(184, 27)
        sup_pasport.TabIndex = 8
        ' 
        ' RadioButton_libyan
        ' 
        RadioButton_libyan.AutoSize = True
        RadioButton_libyan.Location = New Point(124, 33)
        RadioButton_libyan.Name = "RadioButton_libyan"
        RadioButton_libyan.Size = New Size(58, 24)
        RadioButton_libyan.TabIndex = 9
        RadioButton_libyan.TabStop = True
        RadioButton_libyan.Text = "ليبي"
        RadioButton_libyan.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_nonlibyan
        ' 
        RadioButton_nonlibyan.AutoSize = True
        RadioButton_nonlibyan.Location = New Point(37, 34)
        RadioButton_nonlibyan.Name = "RadioButton_nonlibyan"
        RadioButton_nonlibyan.Size = New Size(82, 24)
        RadioButton_nonlibyan.TabIndex = 10
        RadioButton_nonlibyan.TabStop = True
        RadioButton_nonlibyan.Text = "غير ليبي"
        RadioButton_nonlibyan.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_stability2
        ' 
        RadioButton_stability2.AutoSize = True
        RadioButton_stability2.Location = New Point(83, 31)
        RadioButton_stability2.Name = "RadioButton_stability2"
        RadioButton_stability2.Size = New Size(69, 24)
        RadioButton_stability2.TabIndex = 11
        RadioButton_stability2.TabStop = True
        RadioButton_stability2.Text = "مؤقت"
        RadioButton_stability2.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_stability1
        ' 
        RadioButton_stability1.AutoSize = True
        RadioButton_stability1.Location = New Point(157, 31)
        RadioButton_stability1.Name = "RadioButton_stability1"
        RadioButton_stability1.Size = New Size(56, 24)
        RadioButton_stability1.TabIndex = 12
        RadioButton_stability1.TabStop = True
        RadioButton_stability1.Text = "دائم"
        RadioButton_stability1.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_living3
        ' 
        RadioButton_living3.AutoSize = True
        RadioButton_living3.Location = New Point(0, 44)
        RadioButton_living3.Name = "RadioButton_living3"
        RadioButton_living3.Size = New Size(110, 24)
        RadioButton_living3.TabIndex = 13
        RadioButton_living3.TabStop = True
        RadioButton_living3.Text = "سكن مشترك"
        RadioButton_living3.UseVisualStyleBackColor = True
        ' 
        ' RadioButtonliving2
        ' 
        RadioButtonliving2.AutoSize = True
        RadioButtonliving2.Location = New Point(117, 42)
        RadioButtonliving2.Name = "RadioButtonliving2"
        RadioButtonliving2.Size = New Size(53, 24)
        RadioButtonliving2.TabIndex = 14
        RadioButtonliving2.TabStop = True
        RadioButtonliving2.Text = "اجار"
        RadioButtonliving2.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_living1
        ' 
        RadioButton_living1.AutoSize = True
        RadioButton_living1.Location = New Point(176, 41)
        RadioButton_living1.Name = "RadioButton_living1"
        RadioButton_living1.Size = New Size(57, 24)
        RadioButton_living1.TabIndex = 15
        RadioButton_living1.TabStop = True
        RadioButton_living1.Text = "ملك"
        RadioButton_living1.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_stability3
        ' 
        RadioButton_stability3.AutoSize = True
        RadioButton_stability3.Location = New Point(22, 31)
        RadioButton_stability3.Name = "RadioButton_stability3"
        RadioButton_stability3.Size = New Size(55, 24)
        RadioButton_stability3.TabIndex = 16
        RadioButton_stability3.TabStop = True
        RadioButton_stability3.Text = "نازح"
        RadioButton_stability3.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_working
        ' 
        RadioButton_working.AutoSize = True
        RadioButton_working.Location = New Point(119, 26)
        RadioButton_working.Name = "RadioButton_working"
        RadioButton_working.Size = New Size(63, 24)
        RadioButton_working.TabIndex = 17
        RadioButton_working.TabStop = True
        RadioButton_working.Text = "يعمل"
        RadioButton_working.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_nonworking
        ' 
        RadioButton_nonworking.AutoSize = True
        RadioButton_nonworking.Location = New Point(24, 26)
        RadioButton_nonworking.Name = "RadioButton_nonworking"
        RadioButton_nonworking.Size = New Size(76, 24)
        RadioButton_nonworking.TabIndex = 18
        RadioButton_nonworking.TabStop = True
        RadioButton_nonworking.Text = "لا يعمل"
        RadioButton_nonworking.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_sikePressure
        ' 
        CheckBox_sikePressure.AutoSize = True
        CheckBox_sikePressure.Location = New Point(173, 41)
        CheckBox_sikePressure.Name = "CheckBox_sikePressure"
        CheckBox_sikePressure.Size = New Size(68, 24)
        CheckBox_sikePressure.TabIndex = 22
        CheckBox_sikePressure.Text = "ضغط"
        CheckBox_sikePressure.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_sikeSuger
        ' 
        CheckBox_sikeSuger.AutoSize = True
        CheckBox_sikeSuger.Location = New Point(183, 68)
        CheckBox_sikeSuger.Name = "CheckBox_sikeSuger"
        CheckBox_sikeSuger.Size = New Size(58, 24)
        CheckBox_sikeSuger.TabIndex = 23
        CheckBox_sikeSuger.Text = "سكر"
        CheckBox_sikeSuger.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_sikeSly
        ' 
        CheckBox_sikeSly.AutoSize = True
        CheckBox_sikeSly.Location = New Point(132, 133)
        CheckBox_sikeSly.Name = "CheckBox_sikeSly"
        CheckBox_sikeSly.Size = New Size(109, 24)
        CheckBox_sikeSly.TabIndex = 24
        CheckBox_sikeSly.Text = "امراض خبيثة"
        CheckBox_sikeSly.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_sikeHind
        ' 
        CheckBox_sikeHind.AutoSize = True
        CheckBox_sikeHind.Location = New Point(177, 98)
        CheckBox_sikeHind.Name = "CheckBox_sikeHind"
        CheckBox_sikeHind.Size = New Size(64, 24)
        CheckBox_sikeHind.TabIndex = 25
        CheckBox_sikeHind.Text = "اعاقة"
        CheckBox_sikeHind.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_sikeBenignant
        ' 
        CheckBox_sikeBenignant.AutoSize = True
        CheckBox_sikeBenignant.Location = New Point(125, 165)
        CheckBox_sikeBenignant.Name = "CheckBox_sikeBenignant"
        CheckBox_sikeBenignant.Size = New Size(116, 24)
        CheckBox_sikeBenignant.TabIndex = 26
        CheckBox_sikeBenignant.Text = "امراض حميدة"
        CheckBox_sikeBenignant.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_fatherfamily_no
        ' 
        RadioButton_fatherfamily_no.AutoSize = True
        RadioButton_fatherfamily_no.Location = New Point(64, 26)
        RadioButton_fatherfamily_no.Name = "RadioButton_fatherfamily_no"
        RadioButton_fatherfamily_no.Size = New Size(39, 24)
        RadioButton_fatherfamily_no.TabIndex = 33
        RadioButton_fatherfamily_no.TabStop = True
        RadioButton_fatherfamily_no.Text = "لا"
        RadioButton_fatherfamily_no.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_fatherfamily_yes
        ' 
        RadioButton_fatherfamily_yes.AutoSize = True
        RadioButton_fatherfamily_yes.Location = New Point(120, 26)
        RadioButton_fatherfamily_yes.Name = "RadioButton_fatherfamily_yes"
        RadioButton_fatherfamily_yes.Size = New Size(53, 24)
        RadioButton_fatherfamily_yes.TabIndex = 32
        RadioButton_fatherfamily_yes.TabStop = True
        RadioButton_fatherfamily_yes.Text = "نعم"
        RadioButton_fatherfamily_yes.UseVisualStyleBackColor = True
        ' 
        ' Label1
        ' 
        Label1.AutoSize = True
        Label1.Location = New Point(54, 26)
        Label1.Name = "Label1"
        Label1.Size = New Size(145, 20)
        Label1.TabIndex = 36
        Label1.Text = "رقم البطاقة الشخصية"
        ' 
        ' Label2
        ' 
        Label2.AutoSize = True
        Label2.Location = New Point(56, 171)
        Label2.Name = "Label2"
        Label2.Size = New Size(171, 20)
        Label2.TabIndex = 37
        Label2.Text = "الرقم الوطني/رجواز السفر"
        ' 
        ' Label3
        ' 
        Label3.AutoSize = True
        Label3.Location = New Point(56, 226)
        Label3.Name = "Label3"
        Label3.Size = New Size(44, 20)
        Label3.TabIndex = 38
        Label3.Text = "الاسم"
        ' 
        ' Label4
        ' 
        Label4.AutoSize = True
        Label4.Location = New Point(56, 287)
        Label4.Name = "Label4"
        Label4.Size = New Size(42, 20)
        Label4.TabIndex = 39
        Label4.Text = "العمر"
        ' 
        ' Label5
        ' 
        Label5.AutoSize = True
        Label5.Location = New Point(51, 350)
        Label5.Name = "Label5"
        Label5.Size = New Size(76, 20)
        Label5.TabIndex = 40
        Label5.Text = "رقم الهاتف"
        ' 
        ' Label6
        ' 
        Label6.AutoSize = True
        Label6.Location = New Point(49, 406)
        Label6.Name = "Label6"
        Label6.Size = New Size(89, 20)
        Label6.TabIndex = 41
        Label6.Text = "عنوان السكن"
        ' 
        ' Label7
        ' 
        Label7.AutoSize = True
        Label7.Location = New Point(51, 732)
        Label7.Name = "Label7"
        Label7.Size = New Size(75, 20)
        Label7.TabIndex = 42
        Label7.Text = "جهة العمل"
        ' 
        ' Label8
        ' 
        Label8.AutoSize = True
        Label8.Location = New Point(54, 791)
        Label8.Name = "Label8"
        Label8.Size = New Size(108, 20)
        Label8.TabIndex = 43
        Label8.Text = "عدد افراد الاسرة"
        ' 
        ' Label9
        ' 
        Label9.AutoSize = True
        Label9.Location = New Point(386, 24)
        Label9.Name = "Label9"
        Label9.Size = New Size(87, 20)
        Label9.TabIndex = 44
        Label9.Text = "مصدر الدخل"
        ' 
        ' Label_typeneeds
        ' 
        Label_typeneeds.AutoSize = True
        Label_typeneeds.Location = New Point(386, 88)
        Label_typeneeds.Name = "Label_typeneeds"
        Label_typeneeds.Size = New Size(80, 20)
        Label_typeneeds.TabIndex = 46
        Label_typeneeds.Text = "نوع الاحتياج"
        ' 
        ' GroupBox_suptype
        ' 
        GroupBox_suptype.Controls.Add(RadioButton_living1)
        GroupBox_suptype.Controls.Add(RadioButtonliving2)
        GroupBox_suptype.Controls.Add(RadioButton_living3)
        GroupBox_suptype.Location = New Point(45, 462)
        GroupBox_suptype.Name = "GroupBox_suptype"
        GroupBox_suptype.Size = New Size(248, 104)
        GroupBox_suptype.TabIndex = 48
        GroupBox_suptype.TabStop = False
        GroupBox_suptype.Text = "نوع السكن"
        ' 
        ' GroupBox_stability_living
        ' 
        GroupBox_stability_living.Controls.Add(RadioButton_stability1)
        GroupBox_stability_living.Controls.Add(RadioButton_stability2)
        GroupBox_stability_living.Controls.Add(RadioButton_stability3)
        GroupBox_stability_living.Location = New Point(45, 572)
        GroupBox_stability_living.Name = "GroupBox_stability_living"
        GroupBox_stability_living.Size = New Size(219, 77)
        GroupBox_stability_living.TabIndex = 49
        GroupBox_stability_living.TabStop = False
        GroupBox_stability_living.Text = "مدى استقرار السكن"
        ' 
        ' GroupBox_work
        ' 
        GroupBox_work.Controls.Add(RadioButton_working)
        GroupBox_work.Controls.Add(RadioButton_nonworking)
        GroupBox_work.Location = New Point(45, 658)
        GroupBox_work.Name = "GroupBox_work"
        GroupBox_work.Size = New Size(188, 67)
        GroupBox_work.TabIndex = 50
        GroupBox_work.TabStop = False
        GroupBox_work.Text = "الحالة الوظيفية"
        ' 
        ' GroupBox_j
        ' 
        GroupBox_j.Controls.Add(RadioButton_libyan)
        GroupBox_j.Controls.Add(RadioButton_nonlibyan)
        GroupBox_j.Location = New Point(56, 90)
        GroupBox_j.Name = "GroupBox_j"
        GroupBox_j.Size = New Size(188, 67)
        GroupBox_j.TabIndex = 51
        GroupBox_j.TabStop = False
        GroupBox_j.Text = "الجنسية"
        ' 
        ' GroupBox_fatherfamily
        ' 
        GroupBox_fatherfamily.Controls.Add(RadioButton_fatherfamily_yes)
        GroupBox_fatherfamily.Controls.Add(RadioButton_fatherfamily_no)
        GroupBox_fatherfamily.Location = New Point(386, 287)
        GroupBox_fatherfamily.Name = "GroupBox_fatherfamily"
        GroupBox_fatherfamily.Size = New Size(188, 67)
        GroupBox_fatherfamily.TabIndex = 51
        GroupBox_fatherfamily.TabStop = False
        GroupBox_fatherfamily.Text = "هل يوجد معيل للاسرة"
        ' 
        ' GroupBox_helpfamilly
        ' 
        GroupBox_helpfamilly.Controls.Add(RadioButton_helpfamilly_yes)
        GroupBox_helpfamilly.Controls.Add(RadioButton_helpfamilly_no)
        GroupBox_helpfamilly.Location = New Point(386, 362)
        GroupBox_helpfamilly.Name = "GroupBox_helpfamilly"
        GroupBox_helpfamilly.Size = New Size(281, 63)
        GroupBox_helpfamilly.TabIndex = 53
        GroupBox_helpfamilly.TabStop = False
        GroupBox_helpfamilly.Text = "هل تتلقى الاسرة مساعدات من جهة اخرى"
        ' 
        ' RadioButton_helpfamilly_yes
        ' 
        RadioButton_helpfamilly_yes.AutoSize = True
        RadioButton_helpfamilly_yes.Location = New Point(212, 31)
        RadioButton_helpfamilly_yes.Name = "RadioButton_helpfamilly_yes"
        RadioButton_helpfamilly_yes.Size = New Size(53, 24)
        RadioButton_helpfamilly_yes.TabIndex = 32
        RadioButton_helpfamilly_yes.TabStop = True
        RadioButton_helpfamilly_yes.Text = "نعم"
        RadioButton_helpfamilly_yes.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_helpfamilly_no
        ' 
        RadioButton_helpfamilly_no.AutoSize = True
        RadioButton_helpfamilly_no.Location = New Point(157, 31)
        RadioButton_helpfamilly_no.Name = "RadioButton_helpfamilly_no"
        RadioButton_helpfamilly_no.Size = New Size(39, 24)
        RadioButton_helpfamilly_no.TabIndex = 33
        RadioButton_helpfamilly_no.TabStop = True
        RadioButton_helpfamilly_no.Text = "لا"
        RadioButton_helpfamilly_no.UseVisualStyleBackColor = True
        ' 
        ' GroupBoxsike
        ' 
        GroupBoxsike.Controls.Add(RadioButton_sikeyes)
        GroupBoxsike.Controls.Add(RadioButton_sikeno)
        GroupBoxsike.Location = New Point(386, 531)
        GroupBoxsike.Name = "GroupBoxsike"
        GroupBoxsike.Size = New Size(188, 67)
        GroupBoxsike.TabIndex = 54
        GroupBoxsike.TabStop = False
        GroupBoxsike.Text = "هل يوجد مرض"
        ' 
        ' RadioButton_sikeyes
        ' 
        RadioButton_sikeyes.AutoSize = True
        RadioButton_sikeyes.Location = New Point(120, 26)
        RadioButton_sikeyes.Name = "RadioButton_sikeyes"
        RadioButton_sikeyes.Size = New Size(53, 24)
        RadioButton_sikeyes.TabIndex = 32
        RadioButton_sikeyes.TabStop = True
        RadioButton_sikeyes.Text = "نعم"
        RadioButton_sikeyes.UseVisualStyleBackColor = True
        ' 
        ' RadioButton_sikeno
        ' 
        RadioButton_sikeno.AutoSize = True
        RadioButton_sikeno.Location = New Point(60, 26)
        RadioButton_sikeno.Name = "RadioButton_sikeno"
        RadioButton_sikeno.Size = New Size(39, 24)
        RadioButton_sikeno.TabIndex = 33
        RadioButton_sikeno.TabStop = True
        RadioButton_sikeno.Text = "لا"
        RadioButton_sikeno.UseVisualStyleBackColor = True
        ' 
        ' medical_insurance
        ' 
        medical_insurance.Controls.Add(medical_insurance_yes)
        medical_insurance.Controls.Add(medical_insurance_no)
        medical_insurance.Location = New Point(387, 434)
        medical_insurance.Name = "medical_insurance"
        medical_insurance.Size = New Size(188, 67)
        medical_insurance.TabIndex = 55
        medical_insurance.TabStop = False
        medical_insurance.Text = "هل يوجد تامين طبي"
        ' 
        ' medical_insurance_yes
        ' 
        medical_insurance_yes.AutoSize = True
        medical_insurance_yes.Location = New Point(120, 26)
        medical_insurance_yes.Name = "medical_insurance_yes"
        medical_insurance_yes.Size = New Size(53, 24)
        medical_insurance_yes.TabIndex = 32
        medical_insurance_yes.TabStop = True
        medical_insurance_yes.Text = "نعم"
        medical_insurance_yes.UseVisualStyleBackColor = True
        ' 
        ' medical_insurance_no
        ' 
        medical_insurance_no.AutoSize = True
        medical_insurance_no.Location = New Point(60, 24)
        medical_insurance_no.Name = "medical_insurance_no"
        medical_insurance_no.Size = New Size(39, 24)
        medical_insurance_no.TabIndex = 33
        medical_insurance_no.TabStop = True
        medical_insurance_no.Text = "لا"
        medical_insurance_no.UseVisualStyleBackColor = True
        ' 
        ' TextBox_istability
        ' 
        TextBox_istability.BackColor = Color.Azure
        TextBox_istability.Location = New Point(40, 95)
        TextBox_istability.Name = "TextBox_istability"
        TextBox_istability.Size = New Size(119, 27)
        TextBox_istability.TabIndex = 56
        TextBox_istability.Text = "ادخل نوع الاعاقة"
        ' 
        ' Button_register_save
        ' 
        Button_register_save.Location = New Point(490, 814)
        Button_register_save.Name = "Button_register_save"
        Button_register_save.Size = New Size(152, 29)
        Button_register_save.TabIndex = 59
        Button_register_save.Text = "تسجيل بيانات"
        Button_register_save.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_medicine
        ' 
        CheckBox_medicine.AutoSize = True
        CheckBox_medicine.Location = New Point(391, 249)
        CheckBox_medicine.Name = "CheckBox_medicine"
        CheckBox_medicine.Size = New Size(136, 24)
        CheckBox_medicine.TabIndex = 71
        CheckBox_medicine.Text = "مستلزمات صحية"
        CheckBox_medicine.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_eat
        ' 
        CheckBox_eat.AutoSize = True
        CheckBox_eat.Location = New Point(391, 156)
        CheckBox_eat.Name = "CheckBox_eat"
        CheckBox_eat.Size = New Size(103, 24)
        CheckBox_eat.TabIndex = 70
        CheckBox_eat.Text = "مواد غذائية"
        CheckBox_eat.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_clothes
        ' 
        CheckBox_clothes.AutoSize = True
        CheckBox_clothes.Location = New Point(391, 198)
        CheckBox_clothes.Name = "CheckBox_clothes"
        CheckBox_clothes.Size = New Size(72, 24)
        CheckBox_clothes.TabIndex = 69
        CheckBox_clothes.Text = "ملابس"
        CheckBox_clothes.UseVisualStyleBackColor = True
        ' 
        ' CheckBox_money
        ' 
        CheckBox_money.AutoSize = True
        CheckBox_money.Location = New Point(391, 117)
        CheckBox_money.Name = "CheckBox_money"
        CheckBox_money.Size = New Size(62, 24)
        CheckBox_money.TabIndex = 68
        CheckBox_money.Text = "مالي"
        CheckBox_money.UseVisualStyleBackColor = True
        ' 
        ' Button_register_delete
        ' 
        Button_register_delete.Location = New Point(490, 849)
        Button_register_delete.Name = "Button_register_delete"
        Button_register_delete.Size = New Size(152, 29)
        Button_register_delete.TabIndex = 72
        Button_register_delete.Text = "حذف البيانات"
        Button_register_delete.UseVisualStyleBackColor = True
        ' 
        ' GroupBox_sik
        ' 
        GroupBox_sik.Controls.Add(CheckBox_sikePressure)
        GroupBox_sik.Controls.Add(CheckBox_sikeSuger)
        GroupBox_sik.Controls.Add(CheckBox_sikeHind)
        GroupBox_sik.Controls.Add(CheckBox_sikeSly)
        GroupBox_sik.Controls.Add(CheckBox_sikeBenignant)
        GroupBox_sik.Controls.Add(TextBox_istability)
        GroupBox_sik.Location = New Point(386, 604)
        GroupBox_sik.Name = "GroupBox_sik"
        GroupBox_sik.Size = New Size(256, 204)
        GroupBox_sik.TabIndex = 73
        GroupBox_sik.TabStop = False
        GroupBox_sik.Text = "نوع المرض"
        ' 
        ' TextBox_item
        ' 
        TextBox_item.BackColor = Color.Azure
        TextBox_item.Location = New Point(500, 153)
        TextBox_item.Name = "TextBox_item"
        TextBox_item.Size = New Size(101, 27)
        TextBox_item.TabIndex = 74
        TextBox_item.Text = " اسم المادة"
        ' 
        ' item_quntity
        ' 
        item_quntity.BackColor = Color.Azure
        item_quntity.Location = New Point(607, 154)
        item_quntity.Name = "item_quntity"
        item_quntity.Size = New Size(43, 27)
        item_quntity.TabIndex = 75
        item_quntity.Text = "الكمية"
        ' 
        ' TextBox_cloth
        ' 
        TextBox_cloth.BackColor = Color.Azure
        TextBox_cloth.Location = New Point(475, 198)
        TextBox_cloth.Name = "TextBox_cloth"
        TextBox_cloth.Size = New Size(101, 27)
        TextBox_cloth.TabIndex = 76
        TextBox_cloth.Text = "نوع الملابس"
        ' 
        ' cloth_quntity
        ' 
        cloth_quntity.BackColor = Color.Azure
        cloth_quntity.Location = New Point(582, 198)
        cloth_quntity.Name = "cloth_quntity"
        cloth_quntity.Size = New Size(43, 27)
        cloth_quntity.TabIndex = 77
        cloth_quntity.Text = "الكمية"
        ' 
        ' med_quntity
        ' 
        med_quntity.BackColor = Color.Azure
        med_quntity.Location = New Point(644, 249)
        med_quntity.Name = "med_quntity"
        med_quntity.Size = New Size(43, 27)
        med_quntity.TabIndex = 78
        med_quntity.Text = "الكمية"
        ' 
        ' TextBox_med
        ' 
        TextBox_med.BackColor = Color.Azure
        TextBox_med.Location = New Point(537, 249)
        TextBox_med.Name = "TextBox_med"
        TextBox_med.Size = New Size(101, 27)
        TextBox_med.TabIndex = 79
        TextBox_med.Text = "نوع الدواء"
        ' 
        ' TextBox_moneyy
        ' 
        TextBox_moneyy.BackColor = Color.Azure
        TextBox_moneyy.Location = New Point(471, 114)
        TextBox_moneyy.Name = "TextBox_moneyy"
        TextBox_moneyy.Size = New Size(101, 27)
        TextBox_moneyy.TabIndex = 80
        TextBox_moneyy.Text = "قيمة المبلغ"
        ' 
        ' PictureBox1
        ' 
        PictureBox1.Image = My.Resources.Resources.photo_2025_06_26_19_43_38
        PictureBox1.Location = New Point(12, 49)
        PictureBox1.Name = "PictureBox1"
        PictureBox1.Size = New Size(40, 28)
        PictureBox1.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox1.TabIndex = 81
        PictureBox1.TabStop = False
        ' 
        ' PictureBox2
        ' 
        PictureBox2.Image = My.Resources.Resources.photo_2025_06_26_19_36_34
        PictureBox2.Location = New Point(12, 310)
        PictureBox2.Name = "PictureBox2"
        PictureBox2.Size = New Size(40, 28)
        PictureBox2.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox2.TabIndex = 82
        PictureBox2.TabStop = False
        ' 
        ' PictureBox3
        ' 
        PictureBox3.Image = My.Resources.Resources.photo_2025_06_26_20_31_35
        PictureBox3.Location = New Point(12, 198)
        PictureBox3.Name = "PictureBox3"
        PictureBox3.Size = New Size(40, 28)
        PictureBox3.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox3.TabIndex = 83
        PictureBox3.TabStop = False
        ' 
        ' PictureBox4
        ' 
        PictureBox4.Image = My.Resources.Resources.photo_2025_06_26_19_36_26
        PictureBox4.Location = New Point(12, 249)
        PictureBox4.Name = "PictureBox4"
        PictureBox4.Size = New Size(40, 28)
        PictureBox4.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox4.TabIndex = 84
        PictureBox4.TabStop = False
        ' 
        ' PictureBox5
        ' 
        PictureBox5.Image = My.Resources.Resources.photo_2025_06_26_20_27_35
        PictureBox5.Location = New Point(12, 429)
        PictureBox5.Name = "PictureBox5"
        PictureBox5.Size = New Size(40, 28)
        PictureBox5.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox5.TabIndex = 85
        PictureBox5.TabStop = False
        ' 
        ' PictureBox6
        ' 
        PictureBox6.Image = My.Resources.Resources.photo_2025_06_26_19_36_32
        PictureBox6.Location = New Point(12, 373)
        PictureBox6.Name = "PictureBox6"
        PictureBox6.Size = New Size(40, 28)
        PictureBox6.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox6.TabIndex = 86
        PictureBox6.TabStop = False
        ' 
        ' PictureBox7
        ' 
        PictureBox7.Image = My.Resources.Resources.photo_2025_06_26_20_27_40
        PictureBox7.Location = New Point(12, 754)
        PictureBox7.Name = "PictureBox7"
        PictureBox7.Size = New Size(40, 28)
        PictureBox7.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox7.TabIndex = 87
        PictureBox7.TabStop = False
        ' 
        ' PictureBox8
        ' 
        PictureBox8.Image = My.Resources.Resources.photo_2025_06_10_18_07_38
        PictureBox8.Location = New Point(12, 813)
        PictureBox8.Name = "PictureBox8"
        PictureBox8.Size = New Size(40, 28)
        PictureBox8.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox8.TabIndex = 88
        PictureBox8.TabStop = False
        ' 
        ' PictureBox9
        ' 
        PictureBox9.Image = My.Resources.Resources.photo_2025_06_26_20_27_38
        PictureBox9.Location = New Point(340, 46)
        PictureBox9.Name = "PictureBox9"
        PictureBox9.Size = New Size(40, 28)
        PictureBox9.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox9.TabIndex = 89
        PictureBox9.TabStop = False
        ' 
        ' PictureBox10
        ' 
        PictureBox10.Image = My.Resources.Resources.photo_2025_06_26_19_36_411
        PictureBox10.Location = New Point(444, 815)
        PictureBox10.Name = "PictureBox10"
        PictureBox10.Size = New Size(40, 28)
        PictureBox10.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox10.TabIndex = 90
        PictureBox10.TabStop = False
        ' 
        ' PictureBox11
        ' 
        PictureBox11.Image = My.Resources.Resources.photo_2025_06_26_20_05_041
        PictureBox11.Location = New Point(444, 850)
        PictureBox11.Name = "PictureBox11"
        PictureBox11.Size = New Size(40, 28)
        PictureBox11.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox11.TabIndex = 91
        PictureBox11.TabStop = False
        ' 
        ' PictureBox12
        ' 
        PictureBox12.Image = CType(resources.GetObject("PictureBox12.Image"), Image)
        PictureBox12.Location = New Point(576, 0)
        PictureBox12.Name = "PictureBox12"
        PictureBox12.Size = New Size(163, 123)
        PictureBox12.SizeMode = PictureBoxSizeMode.StretchImage
        PictureBox12.TabIndex = 92
        PictureBox12.TabStop = False
        ' 
        ' Subscriber
        ' 
        AutoScaleDimensions = New SizeF(8F, 20F)
        AutoScaleMode = AutoScaleMode.Font
        BackColor = Color.Azure
        ClientSize = New Size(734, 893)
        Controls.Add(PictureBox12)
        Controls.Add(PictureBox11)
        Controls.Add(PictureBox10)
        Controls.Add(PictureBox9)
        Controls.Add(PictureBox8)
        Controls.Add(PictureBox7)
        Controls.Add(PictureBox6)
        Controls.Add(PictureBox5)
        Controls.Add(PictureBox4)
        Controls.Add(PictureBox3)
        Controls.Add(PictureBox2)
        Controls.Add(PictureBox1)
        Controls.Add(TextBox_moneyy)
        Controls.Add(TextBox_med)
        Controls.Add(med_quntity)
        Controls.Add(cloth_quntity)
        Controls.Add(TextBox_cloth)
        Controls.Add(item_quntity)
        Controls.Add(TextBox_item)
        Controls.Add(GroupBox_sik)
        Controls.Add(Button_register_delete)
        Controls.Add(CheckBox_medicine)
        Controls.Add(CheckBox_eat)
        Controls.Add(CheckBox_clothes)
        Controls.Add(CheckBox_money)
        Controls.Add(Button_register_save)
        Controls.Add(medical_insurance)
        Controls.Add(GroupBoxsike)
        Controls.Add(GroupBox_helpfamilly)
        Controls.Add(GroupBox_fatherfamily)
        Controls.Add(GroupBox_j)
        Controls.Add(GroupBox_work)
        Controls.Add(GroupBox_stability_living)
        Controls.Add(GroupBox_suptype)
        Controls.Add(Label_typeneeds)
        Controls.Add(Label9)
        Controls.Add(Label8)
        Controls.Add(Label7)
        Controls.Add(Label6)
        Controls.Add(Label5)
        Controls.Add(Label4)
        Controls.Add(Label3)
        Controls.Add(Label2)
        Controls.Add(Label1)
        Controls.Add(sup_pasport)
        Controls.Add(sup_name)
        Controls.Add(sup_age)
        Controls.Add(sup_number)
        Controls.Add(sup_living)
        Controls.Add(TextBox_workplace)
        Controls.Add(family_total)
        Controls.Add(source_income)
        Controls.Add(sup_id)
        MaximizeBox = False
        MinimizeBox = False
        Name = "Subscriber"
        RightToLeft = RightToLeft.Yes
        RightToLeftLayout = True
        StartPosition = FormStartPosition.CenterScreen
        Text = "شاشة تسجيل مشترك"
        GroupBox_suptype.ResumeLayout(False)
        GroupBox_suptype.PerformLayout()
        GroupBox_stability_living.ResumeLayout(False)
        GroupBox_stability_living.PerformLayout()
        GroupBox_work.ResumeLayout(False)
        GroupBox_work.PerformLayout()
        GroupBox_j.ResumeLayout(False)
        GroupBox_j.PerformLayout()
        GroupBox_fatherfamily.ResumeLayout(False)
        GroupBox_fatherfamily.PerformLayout()
        GroupBox_helpfamilly.ResumeLayout(False)
        GroupBox_helpfamilly.PerformLayout()
        GroupBoxsike.ResumeLayout(False)
        GroupBoxsike.PerformLayout()
        medical_insurance.ResumeLayout(False)
        medical_insurance.PerformLayout()
        GroupBox_sik.ResumeLayout(False)
        GroupBox_sik.PerformLayout()
        CType(PictureBox1, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox2, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox3, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox4, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox5, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox6, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox7, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox8, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox9, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox10, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox11, ComponentModel.ISupportInitialize).EndInit()
        CType(PictureBox12, ComponentModel.ISupportInitialize).EndInit()
        ResumeLayout(False)
        PerformLayout()
    End Sub

    Friend WithEvents sup_id As TextBox
    Friend WithEvents source_income As TextBox
    Friend WithEvents family_total As TextBox
    Friend WithEvents TextBox_workplace As TextBox
    Friend WithEvents sup_living As TextBox
    Friend WithEvents sup_number As TextBox
    Friend WithEvents sup_age As TextBox
    Friend WithEvents sup_name As TextBox
    Friend WithEvents sup_pasport As TextBox
    Friend WithEvents RadioButton_libyan As RadioButton
    Friend WithEvents RadioButton_nonlibyan As RadioButton
    Friend WithEvents RadioButton_stability2 As RadioButton
    Friend WithEvents RadioButton_stability1 As RadioButton
    Friend WithEvents RadioButton_living3 As RadioButton
    Friend WithEvents RadioButtonliving2 As RadioButton
    Friend WithEvents RadioButton_living1 As RadioButton
    Friend WithEvents RadioButton_stability3 As RadioButton
    Friend WithEvents RadioButton_working As RadioButton
    Friend WithEvents RadioButton_nonworking As RadioButton
    Friend WithEvents CheckBox1 As CheckBox
    Friend WithEvents CheckBox2 As CheckBox
    Friend WithEvents CheckBox3 As CheckBox
    Friend WithEvents CheckBox_sikePressure As CheckBox
    Friend WithEvents CheckBox_sikeSuger As CheckBox
    Friend WithEvents CheckBox_sikeSly As CheckBox
    Friend WithEvents CheckBox_sikeHind As CheckBox
    Friend WithEvents CheckBox_sikeBenignant As CheckBox
    Friend WithEvents CheckBox9 As CheckBox
    Friend WithEvents RadioButton_fatherfamily_no As RadioButton
    Friend WithEvents RadioButton_fatherfamily_yes As RadioButton
    Friend WithEvents Label1 As Label
    Friend WithEvents Label2 As Label
    Friend WithEvents Label3 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Label5 As Label
    Friend WithEvents Label6 As Label
    Friend WithEvents Label7 As Label
    Friend WithEvents Label8 As Label
    Friend WithEvents Label9 As Label
    Friend WithEvents Label_typeneeds As Label
    Friend WithEvents GroupBox_suptype As GroupBox
    Friend WithEvents GroupBox_stability_living As GroupBox
    Friend WithEvents GroupBox_work As GroupBox
    Friend WithEvents GroupBox_j As GroupBox
    Friend WithEvents GroupBox_fatherfamily As GroupBox
    Friend WithEvents GroupBox_helpfamilly As GroupBox
    Friend WithEvents RadioButton_helpfamilly_yes As RadioButton
    Friend WithEvents RadioButton_helpfamilly_no As RadioButton
    Friend WithEvents GroupBoxsike As GroupBox
    Friend WithEvents RadioButton_sikeyes As RadioButton
    Friend WithEvents RadioButton_sikeno As RadioButton
    Friend WithEvents medical_insurance As GroupBox
    Friend WithEvents medical_insurance_yes As RadioButton
    Friend WithEvents medical_insurance_no As RadioButton
    Friend WithEvents TextBox_istability As TextBox
    Friend WithEvents Button_register_save As Button
    Friend WithEvents CheckBox_medicine As CheckBox
    Friend WithEvents CheckBox_eat As CheckBox
    Friend WithEvents CheckBox_clothes As CheckBox
    Friend WithEvents CheckBox_money As CheckBox
    Friend WithEvents Button_register_delete As Button
    Friend WithEvents GroupBox_sik As GroupBox
    Friend WithEvents TextBox_item As TextBox
    Friend WithEvents item_quntity As TextBox
    Friend WithEvents TextBox_cloth As TextBox
    Friend WithEvents cloth_quntity As TextBox
    Friend WithEvents med_quntity As TextBox
    Friend WithEvents TextBox_med As TextBox
    Friend WithEvents TextBox_moneyy As TextBox
    Friend WithEvents PictureBox1 As PictureBox
    Friend WithEvents PictureBox2 As PictureBox
    Friend WithEvents PictureBox3 As PictureBox
    Friend WithEvents PictureBox4 As PictureBox
    Friend WithEvents PictureBox5 As PictureBox
    Friend WithEvents PictureBox6 As PictureBox
    Friend WithEvents PictureBox7 As PictureBox
    Friend WithEvents PictureBox8 As PictureBox
    Friend WithEvents PictureBox9 As PictureBox
    Friend WithEvents PictureBox10 As PictureBox
    Friend WithEvents PictureBox11 As PictureBox
    Friend WithEvents PictureBox12 As PictureBox
End Class
