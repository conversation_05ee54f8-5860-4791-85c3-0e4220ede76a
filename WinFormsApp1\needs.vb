Imports System.Data.SqlClient

Public Class Needs


    Dim conn As New SqlConnection("Data Source=DESKTOP-803R29V;Initial Catalog=Project_DB;Integrated Security=True")

    Private Sub Needs_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadNeedsFromSubscribers()
    End Sub

    Public Sub LoadNeedsFromSubscribers()
        Dim dt As New DataTable()
        Try
            conn.Open()
            ' جلب جميع المشتركين وأفراد العائلة لعرضهم في الشبكة (بدون ربط بالاحتياجات)
            Dim query As String = "
SELECT
    s.Subscriber_id AS SubscriberID,
    s.Full_name AS FullName,
    0 AS FamilyCount,
    '' AS ItemType,
    '' AS ItemName,
    0 AS Quantity,
    'subscriber' AS PersonType,
    s.National_id AS NationalID
FROM Subscribers_table s

UNION ALL

SELECT
    f.Subscriber_id AS SubscriberID,
    f.Name AS FullName,
    0 AS FamilyCount,
    '' AS ItemType,
    '' AS ItemName,
    0 AS Quantity,
    'family' AS PersonType,
    CAST(f.Subscriber_id AS VARCHAR) AS NationalID
FROM Family_table f

ORDER BY FullName;"

            Dim adapter As New SqlDataAdapter(query, conn)
            adapter.Fill(dt)
            conn.Close()

            DataGridView_need.DataSource = dt

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ في تحميل البيانات: " & ex.Message)
        End Try
    End Sub

    Private Sub DataGridView_need_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView_need.CellContentClick
        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = DataGridView_need.Rows(e.RowIndex)
            Dim subscriberId As String = row.Cells("SubscriberID").Value.ToString()
            Dim personType As String = row.Cells("PersonType").Value.ToString()

            ' ملء البيانات الأساسية
            TextBox_sub_name.Text = row.Cells("FullName").Value.ToString()

            ' جلب بيانات إضافية حسب نوع الشخص
            Try
                conn.Open()

                If personType = "مشترك" Then
                    ' جلب بيانات المشترك وعدد أفراد العائلة
                    Dim cmdSub As New SqlCommand("SELECT COUNT(*) FROM Family_table WHERE Subscriber_id = @subId", conn)
                    cmdSub.Parameters.AddWithValue("@subId", subscriberId)
                    Dim familyCount As Integer = Convert.ToInt32(cmdSub.ExecuteScalar())
                    family_relate.Text = familyCount.ToString()

                Else ' فرد عائلة
                    ' جلب عدد أفراد العائلة للمشترك الرئيسي
                    Dim cmdFam As New SqlCommand("SELECT COUNT(*) FROM Family_table WHERE Subscriber_id = @subId", conn)
                    cmdFam.Parameters.AddWithValue("@subId", subscriberId)
                    Dim familyCount As Integer = Convert.ToInt32(cmdFam.ExecuteScalar())
                    family_relate.Text = familyCount.ToString()
                End If

                ' جلب الاحتياج الموجود إن وجد
                Dim cmdNeed As New SqlCommand("SELECT n.Need_type, i.Item_name, i.Item_quantity FROM Needs_table n LEFT JOIN Item_table i ON n.Item_id = i.Item_id WHERE n.Subscriber_id = @subId", conn)
                cmdNeed.Parameters.AddWithValue("@subId", subscriberId)
                Dim reader As SqlDataReader = cmdNeed.ExecuteReader()

                If reader.Read() Then
                    ' يوجد احتياج مسجل
                    TextBox_item_type.Text = If(IsDBNull(reader("Need_type")), "", reader("Need_type").ToString())
                    TextBox_item_name.Text = If(IsDBNull(reader("Item_name")), "", reader("Item_name").ToString())
                    TextBox_quantity.Text = If(IsDBNull(reader("Item_quantity")), "0", reader("Item_quantity").ToString())
                Else
                    ' لا يوجد احتياج مسجل - تصفير الحقول
                    TextBox_item_type.Text = ""
                    TextBox_item_name.Text = ""
                    TextBox_quantity.Text = ""
                End If

                reader.Close()
                conn.Close()

            Catch ex As Exception
                If conn.State = ConnectionState.Open Then conn.Close()
                MessageBox.Show("❌ خطأ في جلب البيانات: " & ex.Message)
            End Try
        End If
    End Sub

    Private Sub Button_save_Click(sender As Object, e As EventArgs) Handles Button_save.Click
        Try
            ' التحقق من الحقول المطلوبة
            If String.IsNullOrEmpty(TextBox_sub_name.Text) Or String.IsNullOrEmpty(TextBox_item_type.Text) Or String.IsNullOrEmpty(TextBox_item_name.Text) Then
                MessageBox.Show("❌ يرجى تعبئة جميع الحقول المطلوبة للحفظ.")
                Exit Sub
            End If

            ' التحقق من صحة عدد أفراد العائلة
            Dim familyCount As Integer = 0
            If Not Integer.TryParse(family_relate.Text, familyCount) Then
                MessageBox.Show("❌ يرجى إدخال رقم صحيح لعدد أفراد العائلة.")
                Exit Sub
            End If

            ' جلب معرف المشترك من الصف المحدد
            If DataGridView_need.SelectedRows.Count = 0 Then
                MessageBox.Show("❌ يرجى تحديد شخص من القائمة أولاً.")
                Exit Sub
            End If

            Dim selectedRow As DataGridViewRow = DataGridView_need.SelectedRows(0)
            Dim subscriberId As Integer = Convert.ToInt32(selectedRow.Cells("SubscriberID").Value)

            conn.Open()

            ' إنشاء أو جلب المادة
            Dim itemId As Integer = 0

            ' البحث عن المادة الموجودة
            Dim getItemIdCmd As New SqlCommand("SELECT TOP 1 Item_id FROM Item_table WHERE Item_name = @name AND Item_category = @category", conn)
            getItemIdCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
            getItemIdCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
            Dim resultItem = getItemIdCmd.ExecuteScalar()

            If resultItem Is Nothing Then
                ' إنشاء مادة جديدة
                Dim quantity As Integer = 0
                Integer.TryParse(TextBox_quantity.Text, quantity)

                Dim insertItemCmd As New SqlCommand("INSERT INTO Item_table (Item_name, Item_category, Item_quantity, Expir_date) VALUES (@name, @category, @qty, @date); SELECT SCOPE_IDENTITY();", conn)
                insertItemCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
                insertItemCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
                insertItemCmd.Parameters.AddWithValue("@qty", quantity)
                insertItemCmd.Parameters.AddWithValue("@date", DateTime.Today)
                itemId = Convert.ToInt32(insertItemCmd.ExecuteScalar())
            Else
                itemId = Convert.ToInt32(resultItem)
            End If

            ' التحقق من وجود احتياج مسبق وحذفه
            Dim deleteOldCmd As New SqlCommand("DELETE FROM Needs_table WHERE Subscriber_id = @subId", conn)
            deleteOldCmd.Parameters.AddWithValue("@subId", subscriberId)
            deleteOldCmd.ExecuteNonQuery()

            ' إضافة الاحتياج الجديد (بدون تحديد Need_id لأنه IDENTITY)
            Dim insertNeedCmd As New SqlCommand("INSERT INTO Needs_table (Subscriber_id, Need_type, FamilyNumbe, Item_id) VALUES (@subId, @type, @family, @itemId)", conn)
            insertNeedCmd.Parameters.AddWithValue("@subId", subscriberId)
            insertNeedCmd.Parameters.AddWithValue("@type", TextBox_item_type.Text.Trim())
            insertNeedCmd.Parameters.AddWithValue("@family", familyCount)
            insertNeedCmd.Parameters.AddWithValue("@itemId", itemId)
            insertNeedCmd.ExecuteNonQuery()

            conn.Close()

            MessageBox.Show("✔️ تم حفظ الاحتياج بنجاح.")
            LoadNeedsFromSubscribers()

            ' تصفير الحقول بعد الحفظ الناجح
            TextBox_sub_name.Text = ""
            TextBox_item_type.Text = ""
            TextBox_item_name.Text = ""
            TextBox_quantity.Text = ""
            family_relate.Text = ""

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء الحفظ: " & ex.Message)
        End Try
    End Sub


    Private Sub Butt_delete_Click(sender As Object, e As EventArgs) Handles Butt_delete.Click

        If DataGridView_need.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى تحديد صف لحذفه", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Dim selectedRow As DataGridViewRow = DataGridView_need.SelectedRows(0)
            Dim subscriberId As Integer = Convert.ToInt32(selectedRow.Cells("SubscriberID").Value)
            Dim subscriberName As String = selectedRow.Cells("FullName").Value.ToString()
            Dim personType As String = selectedRow.Cells("PersonType").Value.ToString()

            ' تأكيد الحذف
            Dim result As DialogResult = MessageBox.Show($"هل أنت متأكد من حذف '{subscriberName}' من جميع الجداول؟" & vbCrLf &
                                                       "سيتم حذف البيانات من جداول: المشتركين، العائلة، والاحتياجات",
                                                       "تأكيد الحذف الكامل",
                                                       MessageBoxButtons.YesNo,
                                                       MessageBoxIcon.Warning)

            If result = DialogResult.No Then
                Return
            End If

            conn.Open()

            ' حذف من جدول الاحتياجات أولاً
            Dim deleteNeedCmd As New SqlCommand("DELETE FROM Needs_table WHERE Subscriber_id = @subId", conn)
            deleteNeedCmd.Parameters.AddWithValue("@subId", subscriberId)
            deleteNeedCmd.ExecuteNonQuery()

            ' حذف من جدول العائلة
            Dim deleteFamilyCmd As New SqlCommand("DELETE FROM Family_table WHERE Subscriber_id = @subId", conn)
            deleteFamilyCmd.Parameters.AddWithValue("@subId", subscriberId)
            deleteFamilyCmd.ExecuteNonQuery()

            ' حذف من جدول المشتركين
            Dim deleteSubCmd As New SqlCommand("DELETE FROM Subscribers_table WHERE Subscriber_id = @subId", conn)
            deleteSubCmd.Parameters.AddWithValue("@subId", subscriberId)
            Dim rowsAffected As Integer = deleteSubCmd.ExecuteNonQuery()

            conn.Close()

            If rowsAffected > 0 Then
                MessageBox.Show("✔️ تم حذف جميع بيانات الشخص بنجاح.")
                LoadNeedsFromSubscribers()

                ' تصفير الحقول بعد الحذف
                TextBox_sub_name.Text = ""
                TextBox_item_type.Text = ""
                TextBox_item_name.Text = ""
                TextBox_quantity.Text = ""
                family_relate.Text = ""
            Else
                MessageBox.Show("❌ لم يتم العثور على الشخص المحدد للحذف.")
            End If

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء الحذف: " & ex.Message)
        End Try

    End Sub

    Private Sub But_edit_Click(sender As Object, e As EventArgs) Handles But_edit.Click

        If DataGridView_need.SelectedRows.Count = 0 Then
            MessageBox.Show("يرجى تحديد صف لتعديله", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Dim selectedRow As DataGridViewRow = DataGridView_need.SelectedRows(0)
            Dim subscriberId As Integer = Convert.ToInt32(selectedRow.Cells("SubscriberID").Value)
            Dim personType As String = selectedRow.Cells("PersonType").Value.ToString()

            ' التحقق من صحة البيانات المدخلة
            If String.IsNullOrEmpty(TextBox_sub_name.Text) Or String.IsNullOrEmpty(TextBox_item_type.Text) Then
                MessageBox.Show("❌ يرجى تعبئة جميع الحقول المطلوبة.")
                Return
            End If

            Dim familyCount As Integer = 0
            If Not Integer.TryParse(family_relate.Text, familyCount) Then
                familyCount = 0
            End If

            conn.Open()

            ' تحديث اسم الشخص حسب نوعه
            If personType = "مشترك" Then
                Dim updateSubCmd As New SqlCommand("UPDATE Subscribers_table SET Full_name = @name WHERE Subscriber_id = @subId", conn)
                updateSubCmd.Parameters.AddWithValue("@name", TextBox_sub_name.Text.Trim())
                updateSubCmd.Parameters.AddWithValue("@subId", subscriberId)
                updateSubCmd.ExecuteNonQuery()
            Else ' فرد عائلة
                Dim updateFamCmd As New SqlCommand("UPDATE Family_table SET Name = @name WHERE Subscriber_id = @subId", conn)
                updateFamCmd.Parameters.AddWithValue("@name", TextBox_sub_name.Text.Trim())
                updateFamCmd.Parameters.AddWithValue("@subId", subscriberId)
                updateFamCmd.ExecuteNonQuery()
            End If

            ' إنشاء أو تحديث المادة
            Dim itemId As Integer = 0
            Dim getItemIdCmd As New SqlCommand("SELECT TOP 1 Item_id FROM Item_table WHERE Item_name = @name AND Item_category = @category", conn)
            getItemIdCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
            getItemIdCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
            Dim resultItem = getItemIdCmd.ExecuteScalar()

            If resultItem Is Nothing Then
                ' إنشاء مادة جديدة
                Dim quantity As Integer = 0
                Integer.TryParse(TextBox_quantity.Text, quantity)

                Dim insertItemCmd As New SqlCommand("INSERT INTO Item_table (Item_name, Item_category, Item_quantity, Expir_date) VALUES (@name, @category, @qty, @date); SELECT SCOPE_IDENTITY();", conn)
                insertItemCmd.Parameters.AddWithValue("@name", TextBox_item_name.Text.Trim())
                insertItemCmd.Parameters.AddWithValue("@category", TextBox_item_type.Text.Trim())
                insertItemCmd.Parameters.AddWithValue("@qty", quantity)
                insertItemCmd.Parameters.AddWithValue("@date", DateTime.Today)
                itemId = Convert.ToInt32(insertItemCmd.ExecuteScalar())
            Else
                itemId = Convert.ToInt32(resultItem)
                ' تحديث الكمية
                Dim quantity As Integer = 0
                Integer.TryParse(TextBox_quantity.Text, quantity)
                Dim updateItemCmd As New SqlCommand("UPDATE Item_table SET Item_quantity = @qty WHERE Item_id = @itemId", conn)
                updateItemCmd.Parameters.AddWithValue("@qty", quantity)
                updateItemCmd.Parameters.AddWithValue("@itemId", itemId)
                updateItemCmd.ExecuteNonQuery()
            End If

            ' حذف الاحتياج القديم وإضافة الجديد
            Dim deleteOldCmd As New SqlCommand("DELETE FROM Needs_table WHERE Subscriber_id = @subId", conn)
            deleteOldCmd.Parameters.AddWithValue("@subId", subscriberId)
            deleteOldCmd.ExecuteNonQuery()

            Dim insertNeedCmd As New SqlCommand("INSERT INTO Needs_table (Subscriber_id, Need_type, FamilyNumbe, Item_id) VALUES (@subId, @type, @family, @itemId)", conn)
            insertNeedCmd.Parameters.AddWithValue("@subId", subscriberId)
            insertNeedCmd.Parameters.AddWithValue("@type", TextBox_item_type.Text.Trim())
            insertNeedCmd.Parameters.AddWithValue("@family", familyCount)
            insertNeedCmd.Parameters.AddWithValue("@itemId", itemId)
            insertNeedCmd.ExecuteNonQuery()

            conn.Close()

            MessageBox.Show("✔️ تم تعديل البيانات بنجاح.")
            LoadNeedsFromSubscribers()

            ' تصفير الحقول بعد التعديل الناجح
            TextBox_sub_name.Text = ""
            TextBox_item_type.Text = ""
            TextBox_item_name.Text = ""
            TextBox_quantity.Text = ""
            family_relate.Text = ""

        Catch ex As Exception
            If conn.State = ConnectionState.Open Then conn.Close()
            MessageBox.Show("❌ خطأ أثناء التعديل: " & ex.Message)
        End Try

    End Sub

End Class